import prisma from "~/server/db/prisma";
import { ChargePointError, MonitoringEvent } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { env } from "~/env";

export async function getChargepointErrors() {
  const errors: Pick<ChargePointError, "chargePointId" | "error">[] = await prisma.$queryRawUnsafe(`
        SELECT cpe.chargePointId,
               cpe.error
        FROM ChargePointError cpe
        WHERE cpe.createdAt = (SELECT MAX(createdAt) FROM ChargePointError)
          and error not like 'eth interface had 5 link%';
    `);

  return errors;
}

export async function checkZerokWh() {
  const events: Pick<MonitoringEvent, "evseId" | "lastevent">[] = await prisma.$queryRawUnsafe(`
    SELECT Start_datetime, CDR_ID, Charge_Point_ID as 'evseId', Charge_Point_Address,
           count(*) as 'Muster gefunden', UNIX_TIMESTAMP(max(End_datetime)) as "lastevent"
    FROM (
           SELECT t.*,
                  -- <PERSON><PERSON>hle aufeinanderfolgende Nullwerte direkt vor dem aktuellen Eintrag
                  CASE
                    WHEN LAG(volume, 1) OVER (PARTITION BY Charge_Point_ID ORDER BY End_datetime) = 0
                    AND LAG(volume, 2) OVER (PARTITION BY Charge_Point_ID ORDER BY End_datetime) = 0
                AND LAG(volume, 3) OVER (PARTITION BY Charge_Point_ID ORDER BY End_datetime) = 0
             THEN 1
               ELSE 0
           END AS has_3_consecutive_zeros_before
           FROM Cdr t
           WHERE Date(Start_datetime) > DATE_SUB(CURDATE(), INTERVAL 3 DAY)
         ) subquery
    WHERE subquery.volume = 0
      AND subquery.has_3_consecutive_zeros_before = 1
    GROUP BY Charge_Point_ID;`);

  const evses = await prisma.evse.findMany({
    where: { evse_id: { in: events.map((event) => event.evseId) } },
    select: { evse_id: true, chargePointId: true },
  });

  const entries = events.map((event) => {
    return {
      message: "0 kWh Event detected",
      evseId: event.evseId,
      type: "0_KWH",
      lastevent: event.lastevent,
      href: `${env.LONGSHIP_PORTAL_URL}/chargers/${evses.find(
        (evse) => evse.evse_id == event.evseId,
      )?.chargePointId}`,
    };
  });

  await prisma.monitoringEvent.createMany({ data: entries, skipDuplicates: true });
}

export async function checkAdhocPaymentIntents() {
  Logger(
    "Checking Adhoc CDRs for missing PaymentIntents",
    "Error Monitoring",
    "cron",
    LogType.INFO,
  );

  // Find CDRs with TariffName "Adhoc" or "Eulektro Adhoc" from the last 7 days

  const cdrs: { CDR_ID: string; Charge_Point_ID: string; End_datetime: Date }[] =
    await prisma.$queryRawUnsafe(`
    SELECT c.CDR_ID, c.Charge_Point_ID, c.End_datetime
    FROM Cdr c
    LEFT JOIN PaymentIntent pi ON c.CDR_ID = pi.cdrId
    WHERE c.Tariff_Name IN ('Adhoc', 'Eulektro Adhoc')
      AND c.End_datetime >= DATE_SUB(NOW(), INTERVAL 2 DAY)
      AND pi.id IS NULL
  `);

  Logger(
    `Found ${cdrs.length} Adhoc CDRs without PaymentIntents`,
    "Error Monitoring",
    "cron",
    LogType.INFO,
  );

  if (cdrs.length === 0) {
    return;
  }

  // Create monitoring events for each CDR without a PaymentIntent
  const entries = cdrs.map((cdr) => {
    return {
      message: `Adhoc CDR without PaymentIntent (${cdr.CDR_ID})`,
      evseId: cdr.Charge_Point_ID || "unknown",
      type: "ADHOC_NOT_CAPTURED",
      href: `${env.LONGSHIP_PORTAL_URL}/cdrs/${cdr.CDR_ID}`,
      lastevent: BigInt(Math.floor(cdr.End_datetime.getTime() / 1000)), // Convert to UNIX timestamp
      cdrId: cdr.CDR_ID,
    };
  });

  await prisma.monitoringEvent.createMany({ data: entries, skipDuplicates: true });

  Logger(
    `Created ${entries.length} monitoring events for Adhoc CDRs without PaymentIntents`,
    "Error Monitoring",
    "cron",
    LogType.INFO,
  );

  //markiere das Event als erledigt
  await prisma.$queryRawUnsafe(`UPDATE MonitoringEvent me
                                    INNER JOIN PaymentIntent pi ON me.cdrId = pi.cdrId
                                    SET me.resolved = 1
                                  WHERE pi.cdrId IS NOT NULL;
    `);
}
