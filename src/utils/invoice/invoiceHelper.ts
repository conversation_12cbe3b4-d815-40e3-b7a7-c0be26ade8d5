import type { Cdr } from "@prisma/client";
import type { CdrWithIncludes } from "~/server/invoice/calculateCost";
import fs from "fs";
import { KindOfInvoice } from "@prisma/client";

export enum KindToCount {
  BILLABLE,
  NOTBILLABLE,
  ALL,
}

export const checkAndCreateDir = (dir: string) => {
  // Überprüfen, ob Directory existiert, falls nicht, lege es an
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  return;
};

export const getTypeOfInvoiceShortcut = (kind: KindOfInvoice) => {
  switch (kind) {
    case KindOfInvoice.INVOICE:
      return "RE";
    case KindOfInvoice.STORNO:
      return "RE";
    case KindOfInvoice.CREDIT:
      return "GU";
    case KindOfInvoice.CREDIT_STORNO:
      return "GU";
    default:
      "RE";
  }
};

export const SummOfKwhFromCdrs = (
  cdrs: CdrWithIncludes[],
  kindToCount = KindToCount.ALL,
): number => {
  return cdrs.reduce((acc, cdr) => {
    switch (kindToCount) {
      case KindToCount.BILLABLE: {
        if (
          cdr?.Volume &&
          cdr.tarif &&
          cdr.Volume >= cdr.tarif.minChargingEnergy &&
          cdr?.DurationInSec != null &&
          cdr.DurationInSec >= cdr.tarif.minChargingTime
        ) {
          return acc + (cdr.Volume || 0);
        }
        return acc;
      }
      case KindToCount.NOTBILLABLE: {
        if (
          (cdr?.Volume && cdr.tarif && cdr.Volume < cdr.tarif.minChargingEnergy) ||
          (cdr?.DurationInSec != null && cdr.tarif && cdr.DurationInSec < cdr.tarif.minChargingTime)
        ) {
          return acc + (cdr.Volume || 0);
        }
        return acc;
      }
      case KindToCount.ALL: {
        return acc + (cdr.Volume || 0);
      }
    }
  }, 0);
};

export const SummOfSessionFromCdrs = (
  cdrs: CdrWithIncludes[],
  kindToCount = KindToCount.ALL,
): number => {
  return cdrs.reduce((acc, cdr) => {
    switch (kindToCount) {
      case KindToCount.BILLABLE: {
        if (
          cdr?.Volume &&
          cdr.tarif &&
          cdr.Volume >= cdr.tarif.minChargingEnergy &&
          cdr?.DurationInSec != null &&
          cdr.DurationInSec >= cdr.tarif.minChargingTime
        ) {
          return acc + 1;
        }
        return acc;
      }
      case KindToCount.NOTBILLABLE: {
        if (
          (cdr?.Volume && cdr.tarif && cdr.Volume < cdr.tarif.minChargingEnergy) ||
          (cdr?.DurationInSec != null && cdr.tarif && cdr.DurationInSec < cdr.tarif.minChargingTime)
        ) {
          return acc + 1;
        }
        return acc;
      }
      case KindToCount.ALL: {
        return acc + 1;
      }
    }
  }, 0);
};

export const findMaxEndDatetime = (cdrs: Cdr[]) => {
  if (!cdrs || cdrs[0] == undefined) {
    return;
  }
  let maxCdr = cdrs[0];
  for (const cdr of cdrs) {
    if (cdr.End_datetime > maxCdr.End_datetime) {
      maxCdr = cdr;
    }
  }
  return maxCdr.End_datetime;
};

export const findMinEndDatetime = (cdrs: Cdr[]) => {
  if (!cdrs || cdrs[0] == undefined) {
    return;
  }
  let minCdr: Cdr = cdrs[0];
  for (const cdr of cdrs) {
    if (cdr.Start_datetime < minCdr.Start_datetime) {
      minCdr = cdr;
    }
  }
  return minCdr?.Start_datetime;
};

export const findMinStartDatetime = (cdrs: Cdr[]) => {
  if (!cdrs || cdrs.length === 0) {
    return;
  }

  const validCdrs = cdrs.filter((cdr) => cdr !== undefined && cdr !== null);
  if (validCdrs.length === 0) {
    return;
  }

  let minCdr: Cdr = validCdrs[0] as Cdr;
  for (const cdr of validCdrs) {
    if (cdr.Start_datetime < minCdr.Start_datetime) {
      minCdr = cdr;
    }
  }

  return minCdr.Start_datetime;
};
