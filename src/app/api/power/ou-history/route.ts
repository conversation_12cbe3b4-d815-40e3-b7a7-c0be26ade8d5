import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.selectedOu) {
      return NextResponse.json({ error: "No session or selected OU" }, { status: 401 });
    }

    const body = await request.json();
    const { start, end } = body;
    
    if (!start || !end) {
      return NextResponse.json({ error: "Start and end dates are required" }, { status: 400 });
    }
    
    // Get all OUs below the selected OU (including the selected OU itself)
    const ous = await getOusBelowOu(session.user.selectedOu);
    const ouIds = ous.map(ou => ou.id);

    // Fetch power data for the selected OU and its children
    const result = await prisma.historyPowerByOu.findMany({
      where: {
        ouId: { in: ouIds },
        timestamp: {
          gte: new Date(start),
          lte: new Date(end)
        },
      },
      orderBy: {
        timestamp: "asc",
      },
      select: {
        timestamp: true,
        power: true,
        ouId: true,
      },
    });

    console.log(`Found ${result.length} power records for OUs: ${ouIds.join(', ')}`);
    console.log(`Date range: ${new Date(start).toISOString()} - ${new Date(end).toISOString()}`);

    if (result.length > 0) {
      console.log('Sample records:', result.slice(0, 3).map(r => ({
        timestamp: r.timestamp.toISOString(),
        power: r.power,
        ouId: r.ouId
      })));
    }

    // If no data found, return the existing data structure but with debug info
    if (result.length === 0) {
      return NextResponse.json({
        power: [],
        debug: {
          ouIds,
          dateRange: { start: new Date(start).toISOString(), end: new Date(end).toISOString() },
          message: "No power data found for the specified OUs and date range"
        }
      });
    }

    // Group by timestamp (ignoring seconds) and sum power values from multiple OUs
    const powerByTimestamp = new Map<number, number>();

    result.forEach(record => {
      // Round timestamp to nearest minute (ignore seconds)
      const date = new Date(record.timestamp);
      date.setSeconds(0, 0); // Set seconds and milliseconds to 0
      const roundedTimestamp = date.getTime();

      const currentPower = powerByTimestamp.get(roundedTimestamp) || 0;
      powerByTimestamp.set(roundedTimestamp, currentPower + record.power);
    });

    console.log(`Aggregated ${powerByTimestamp.size} unique timestamps (rounded to minutes)`);

    // Fill gaps with 0 values
    const intervals: Array<[number, number]> = [];

    if (powerByTimestamp.size > 0) {
      // Get the actual data range
      const timestamps = Array.from(powerByTimestamp.keys()).sort((a, b) => a - b);
      const firstDataTimestamp = timestamps[0];
      const lastDataTimestamp = timestamps[timestamps.length - 1];

      console.log('Data range:', {
        firstData: new Date(firstDataTimestamp).toISOString(),
        lastData: new Date(lastDataTimestamp).toISOString(),
        requestedStart: new Date(start).toISOString(),
        requestedEnd: new Date(end).toISOString()
      });

      // Generate intervals from first to last data point, filling gaps with 0
      const current = new Date(firstDataTimestamp);
      const endDate = new Date(lastDataTimestamp);

      while (current <= endDate) {
        const timestamp = current.getTime();
        const power = powerByTimestamp.get(timestamp) || 0;
        intervals.push([timestamp, power / 1000]); // Convert to kW

        // Add 5 minutes (since data is typically in 5-minute intervals)
        current.setMinutes(current.getMinutes() + 5);
      }
    }

    console.log(`Generated ${intervals.length} intervals (including zeros)`);
    if (intervals.length > 0) {
      const nonZeroIntervals = intervals.filter(([, power]) => power > 0);
      console.log(`${nonZeroIntervals.length} intervals with power > 0`);
      console.log('First interval:', {
        timestamp: new Date(intervals[0][0]).toISOString(),
        power: intervals[0][1]
      });
      console.log('Last interval:', {
        timestamp: new Date(intervals[intervals.length - 1][0]).toISOString(),
        power: intervals[intervals.length - 1][1]
      });
    }

    // Fetch energy stock prices for the same time range
    const stockPrices = await prisma.energyStockPrice.findMany({
      where: {
        timestamp: {
          gte: new Date(start),
          lte: new Date(end)
        },
      },
      orderBy: {
        timestamp: "asc",
      },
      select: {
        timestamp: true,
        amount: true,
      },
    });

    console.log(`Found ${stockPrices.length} stock price records`);

    // Convert stock prices to the same format
    const priceIntervals: Array<[number, number]> = stockPrices.map(price => [
      price.timestamp.getTime(),
      price.amount
    ]);

    const data = {
      power: intervals,
      price: priceIntervals,
    };

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching OU power history:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
