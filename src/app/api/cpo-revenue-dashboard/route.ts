import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice, KindOfInvoice } from "@prisma/client";
import type { CPORevenueDashboardData, CPORevenueData, CPOInvoiceDetail } from "../finance-dashboard/route";

function getMonthKey(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
}

function isOnboardingFee(title: string, description?: string): boolean {
  const onboardingKeywords = [
    "kartenbestellgebühr",
    "onboarding",
    "einrichtung",
    "setup",
    "aktivierung",
    "ersteinrichtung",
    "Einmaliger Serviceeinsatz",
  ];

  const searchText = `${title} ${description || ""}`.toLowerCase();
  return onboardingKeywords.some((keyword) => searchText.includes(keyword));
}

function calculateYearForecast(
  onboardingRevenue: number,
  recurringRevenue: number,
  monthlyData: { month: string; recurringRevenue: number }[]
): number {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  // Filter data for current year only
  const currentYearData = monthlyData.filter((item) =>
    item.month.startsWith(currentYear.toString()) && item.recurringRevenue > 0
  );

  if (currentYearData.length === 0) return onboardingRevenue;

  // Calculate average monthly recurring revenue from actual data
  const totalRecurringRevenue = currentYearData.reduce((sum, item) => sum + item.recurringRevenue, 0);
  const avgMonthlyRecurring = totalRecurringRevenue / currentYearData.length;

  // Forecast remaining months with recurring revenue only
  const remainingMonths = Math.max(0, 12 - currentMonth);
  const forecastRecurringRevenue = totalRecurringRevenue + (avgMonthlyRecurring * remainingMonths);

  // Total forecast = onboarding (already earned, one-time) + forecasted recurring
  return onboardingRevenue + forecastRecurringRevenue;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  try {
    // Build where clause for CPO invoices - no OU filter, show all CPOs for admins
    const whereClause: any = {
      contact: {
        cpo: true, // Only CPO contacts
      },
      stateOfInvoice: {
        in: [StateOfInvoice.CREATED, StateOfInvoice.PAID], // Include both sent and paid invoices
      },
      // Exclude cancelled invoices and storno invoices
      kindOfInvoice: {
        notIn: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
      },
      // Exclude invoices that have been cancelled by a storno
      invoiceChilds: {
        none: {
          kindOfInvoice: {
            in: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
          },
        },
      },
    };

    // Get all CPO invoices (both regular and credit notes)
    const cpoInvoices = await prisma.invoice.findMany({
      where: whereClause,
      include: {
        contact: true,
        invoicePositions: true,
        contract: true,
      },
      orderBy: {
        invoiceDate: "desc",
      },
    });

    // Group invoices by CPO contact
    const cpoMap = new Map<
      string,
      {
        contact: any;
        invoices: any[];
        creditNotes: any[];
        monthlyData: Map<string, { revenue: number; creditNotes: number; invoiced: number; recurringRevenue: number }>;
      }
    >();

    for (const invoice of cpoInvoices) {
      if (!invoice.contact) continue;

      const cpoId = invoice.contact.id;
      if (!cpoMap.has(cpoId)) {
        cpoMap.set(cpoId, {
          contact: invoice.contact,
          invoices: [],
          creditNotes: [],
          monthlyData: new Map(),
        });
      }

      const cpoData = cpoMap.get(cpoId)!;

      if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
        cpoData.creditNotes.push(invoice);
      } else {
        cpoData.invoices.push(invoice);
      }

      // Add to monthly breakdown
      if (invoice.invoiceDate) {
        const monthKey = getMonthKey(invoice.invoiceDate);
        if (!cpoData.monthlyData.has(monthKey)) {
          cpoData.monthlyData.set(monthKey, { revenue: 0, creditNotes: 0, invoiced: 0, recurringRevenue: 0 });
        }

        const monthData = cpoData.monthlyData.get(monthKey)!;
        if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
          monthData.creditNotes += invoice.sumGross;
          // Don't subtract credit notes from revenue - they are separate transactions
        } else {
          monthData.invoiced += invoice.sumGross;
          monthData.revenue += invoice.sumGross;

          // Calculate recurring revenue for this invoice
          let invoiceRecurringRevenue = 0;
          for (const position of invoice.invoicePositions) {
            if (!isOnboardingFee(position.title, position.description)) {
              invoiceRecurringRevenue += position.sumGross;
            }
          }
          monthData.recurringRevenue += invoiceRecurringRevenue;
        }
      }
    }

    // Process each CPO's data
    const cpos: CPORevenueData[] = [];
    let totalCreditNotes = 0;
    let totalInvoiced = 0;
    let totalOnboarding = 0;
    let totalRecurring = 0;

    for (const [cpoId, cpoData] of cpoMap.entries()) {
      const cpoRevenue = cpoData.invoices.reduce((sum, inv) => sum + inv.sumGross, 0);
      const cpoCreditNotes = cpoData.creditNotes.reduce((sum, inv) => sum + inv.sumGross, 0);

      // Calculate onboarding vs recurring revenue
      let onboardingRevenue = 0;
      let recurringRevenue = 0;

      for (const invoice of cpoData.invoices) {
        for (const position of invoice.invoicePositions) {
          if (isOnboardingFee(position.title, position.description)) {
            onboardingRevenue += position.sumGross;
          } else {
            recurringRevenue += position.sumGross;
          }
        }
      }

      // Convert monthly data to array
      const monthlyBreakdown = Array.from(cpoData.monthlyData.entries())
        .map(([month, data]) => ({
          month,
          revenue: data.revenue,
          creditNotes: data.creditNotes,
          invoiced: data.invoiced,
          recurringRevenue: data.recurringRevenue,
        }))
        .sort((a, b) => b.month.localeCompare(a.month));

      const yearForecast = calculateYearForecast(onboardingRevenue, recurringRevenue, monthlyBreakdown);
      // Don't subtract credit notes from revenue - they are separate transactions

      // Prepare invoice details
      const invoiceDetails: CPOInvoiceDetail[] = cpoData.invoices.map(invoice => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: invoice.invoiceDate,
        sumGross: invoice.sumGross,
        kindOfInvoice: invoice.kindOfInvoice,
        isOnboarding: invoice.invoicePositions.some((pos: any) =>
          isOnboardingFee(pos.title, pos.description)
        ),
        positions: invoice.invoicePositions.map((pos: any) => ({
          title: pos.title,
          description: pos.description,
          amount: pos.amount,
          unitPrice: pos.unitPrice,
          sumGross: pos.sumGross,
        })),
      }));

      // Prepare credit note details
      const creditNoteDetails: CPOInvoiceDetail[] = cpoData.creditNotes.map(invoice => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: invoice.invoiceDate,
        sumGross: invoice.sumGross,
        kindOfInvoice: invoice.kindOfInvoice,
        isOnboarding: false,
        positions: invoice.invoicePositions.map((pos: any) => ({
          title: pos.title,
          description: pos.description,
          amount: pos.amount,
          unitPrice: pos.unitPrice,
          sumGross: pos.sumGross,
        })),
      }));

      cpos.push({
        cpoId,
        cpoName: cpoData.contact.companyName || cpoData.contact.name || "Unbekannt",
        totalCreditNotes: cpoCreditNotes,
        totalInvoiced: cpoRevenue,
        onboardingRevenue,
        recurringRevenue,
        yearForecast,
        monthlyBreakdown,
        invoiceDetails,
        creditNoteDetails,
      });

      totalCreditNotes += cpoCreditNotes;
      totalInvoiced += cpoRevenue;
      totalOnboarding += onboardingRevenue;
      totalRecurring += recurringRevenue;
    }

    // Sort CPOs by total invoiced (descending)
    cpos.sort((a, b) => b.totalInvoiced - a.totalInvoiced);

    const result: CPORevenueDashboardData = {
      cpos,
      totalCreditNotes,
      totalInvoiced,
      totalOnboarding,
      totalRecurring,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching CPO revenue dashboard data:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
