import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";

const CreateUserGroupSchema = z.object({
  name: z.string().min(1, "Name ist erforderlich"),
  description: z.string().optional(),
  ouId: z.string(),
});

const UpdateUserGroupSchema = z.object({
  name: z.string().min(1, "Name ist erforderlich").optional(),
  description: z.string().optional(),
});

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const ouId = searchParams.get("ouId");

  try {
    let whereClause: any = {};

    // If ouId is provided, use it (but validate access)
    if (ouId) {
      // Check if user has access to this OU
      if (session.user.role !== Role.ADMIN && ouId !== session.user.selectedOu.id) {
        return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
      }
      whereClause.ouId = ouId;
    } else {
      // If not admin, filter by user's OU
      if (session.user.role !== Role.ADMIN) {
        whereClause.ouId = session.user.selectedOu.id;
      }
    }

    const userGroups = await prisma.userGroup.findMany({
      where: whereClause,
      include: {
        ou: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            users: true,
            physicalCards: true,
            companyTarifs: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(userGroups);
  } catch (error) {
    console.error("Error fetching user groups:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const result = CreateUserGroupSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.issues }, { status: 400 });
    }

    const { name, description, ouId } = result.data;

    // Check if user has permission to create user group in this OU
    if (session.user.role !== Role.ADMIN && ouId !== session.user.selectedOu.id) {
      return NextResponse.json({ error: "Unauthorized for this OU" }, { status: 403 });
    }

    // Check if user group with same name already exists in this OU
    const existingUserGroup = await prisma.userGroup.findFirst({
      where: {
        name,
        ouId,
      },
    });

    if (existingUserGroup) {
      return NextResponse.json(
        { error: "Eine Nutzergruppe mit diesem Namen existiert bereits in dieser OU" },
        { status: 409 },
      );
    }

    const userGroup = await prisma.userGroup.create({
      data: {
        name,
        description,
        ouId,
      },
      include: {
        ou: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            users: true,
            physicalCards: true,
            companyTarifs: true,
          },
        },
      },
    });

    return NextResponse.json(userGroup, { status: 201 });
  } catch (error) {
    console.error("Error creating user group:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
