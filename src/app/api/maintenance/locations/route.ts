import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const ouId = searchParams.get("ouId") || session.user.selectedOu.id;

  try {
    // Get all child OUs for filtering
    const childOus = await prisma.ou.findMany({
      where: {
        OR: [
          { id: ouId },
          { parentId: ouId }
        ]
      },
      select: { id: true }
    });

    const ouIds = childOus.map(ou => ou.id);

    const locations = await prisma.location.findMany({
      where: {
        ouId: { in: ouIds }
      },
      select: {
        id: true,
        name: true,
        city: true,
        street: true,
        evses: {
          select: {
            uid: true,
            evse_id: true,
            status: true
          },
          orderBy: {
            evse_id: 'asc'
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json(locations);
  } catch (error) {
    console.error("Error fetching locations:", error);
    return NextResponse.json(
      { error: "Failed to fetch locations" },
      { status: 500 }
    );
  }
}
