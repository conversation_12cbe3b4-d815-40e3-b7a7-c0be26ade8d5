import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, MaintenanceTargetType, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const ouId = searchParams.get("ouId") || session.user.selectedOu.id;
  const limit = parseInt(searchParams.get("limit") || "50");
  const offset = parseInt(searchParams.get("offset") || "0");

  try {
    // Get all child OUs for filtering
    const childOus = await getOusBelowOu(session.user.selectedOu);

    const ouIds = childOus.map((ou) => ou.id);

    const [records, total] = await Promise.all([
      prisma.maintenanceRecord.findMany({
        where: {
          ouId: { in: ouIds },
        },
        include: {
          category: true,
          user: {
            select: {
              id: true,
              name: true,
              lastName: true,
            },
          },
          location: {
            select: {
              id: true,
              name: true,
            },
          },
          evse: {
            select: {
              uid: true,
              evse_id: true,
            },
          },
          ou: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          date: "desc",
        },
        take: limit,
        skip: offset,
      }),
      prisma.maintenanceRecord.count({
        where: {
          ouId: { in: ouIds },
        },
      }),
    ]);

    return NextResponse.json({
      records,
      total,
      limit,
      offset,
    });
  } catch (error) {
    console.error("Error fetching maintenance records:", error);
    return NextResponse.json({ error: "Failed to fetch maintenance records" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { date, description, notes, targetType, locationId, evseId, connectorId, categoryId } =
      await request.json();

    if (!date || !description || !targetType || !categoryId) {
      return NextResponse.json(
        { error: "Date, description, targetType, and categoryId are required" },
        { status: 400 },
      );
    }

    // Validate targetType
    if (!Object.values(MaintenanceTargetType).includes(targetType)) {
      return NextResponse.json({ error: "Invalid targetType" }, { status: 400 });
    }

    // Validate category exists
    const category = await prisma.maintenanceCategory.findUnique({
      where: { id: categoryId },
    });

    if (!category) {
      return NextResponse.json({ error: "Category not found" }, { status: 404 });
    }

    // Calculate next due date for DGUV-V3 (50 weeks)
    let nextDueDate = null;
    if (category.name === "DGUV-V3") {
      const maintenanceDate = new Date(date);
      nextDueDate = new Date(maintenanceDate);
      nextDueDate.setDate(nextDueDate.getDate() + 50 * 7); // 50 weeks
    }

    const record = await prisma.maintenanceRecord.create({
      data: {
        date: new Date(date),
        description,
        notes,
        targetType,
        locationId: locationId || null,
        evseId: evseId || null,
        connectorId: connectorId || null,
        categoryId,
        ouId: session.user.selectedOu.id,
        userId: session.user.id,
        nextDueDate,
      },
      include: {
        category: true,
        user: {
          select: {
            id: true,
            name: true,
            lastName: true,
          },
        },
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        evse: {
          select: {
            uid: true,
            evse_id: true,
          },
        },
      },
    });
    await createSystemNotificationForAdmins({
      nachricht: `Neuer Wartungseintrag erstellt: ${record.description}`,
      type: NotificationType.INFO,
    });
    return NextResponse.json(record, { status: 201 });
  } catch (error) {
    console.error("Error creating maintenance record:", error);
    return NextResponse.json({ error: "Failed to create maintenance record" }, { status: 500 });
  }
}
