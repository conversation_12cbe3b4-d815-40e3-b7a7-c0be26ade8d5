"use client";

import React, { useState } from "react";
import TermsOfServiceAgreement from "~/component/TermsOfServiceAgreement";
import Button from "~/component/button";

const AGBDemoPage = () => {
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showError, setShowError] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleSubmit = () => {
    if (!termsAccepted) {
      setShowError(true);
      return;
    }
    setShowError(false);
    setFormSubmitted(true);
  };

  const handleTermsChange = (checked: boolean) => {
    setTermsAccepted(checked);
    if (checked) {
      setShowError(false);
    }
  };

  if (formSubmitted) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="w-full max-w-md rounded-xl bg-white p-8 shadow-lg">
          <div className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <svg
                className="h-8 w-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h2 className="mb-2 text-xl font-bold text-gray-900">
              AGB erfolgreich akzeptiert!
            </h2>
            <p className="text-gray-600">
              Sie haben die Allgemeinen Geschäftsbedingungen akzeptiert und können nun fortfahren.
            </p>
            <Button
              className="mt-4"
              onClick={() => {
                setFormSubmitted(false);
                setTermsAccepted(false);
              }}
            >
              Zurück zur Demo
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="mx-auto max-w-4xl px-4">
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-3xl font-bold text-primary">
            AGB-Komponente Demo
          </h1>
          <p className="text-gray-600">
            Demonstration der TermsOfServiceAgreement Komponente für die Registrierung
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          {/* Variante 1: Mit Link zu externen AGB */}
          <div className="rounded-xl bg-white p-6 shadow-lg">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              Variante 1: Mit Link zu AGB
            </h2>
            <p className="mb-4 text-gray-600">
              Standard-Implementierung mit Link zu einer externen AGB-Seite.
            </p>
            
            <TermsOfServiceAgreement
              checked={termsAccepted}
              onChange={handleTermsChange}
              error={showError ? "Sie müssen die AGB akzeptieren, um fortzufahren." : undefined}
              termsLink="/agb"
            />
          </div>

          {/* Variante 2: Mit vollständigem Text */}
          <div className="rounded-xl bg-white p-6 shadow-lg">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              Variante 2: Mit vollständigem AGB-Text
            </h2>
            <p className="mb-4 text-gray-600">
              Zeigt den vollständigen AGB-Text in einem scrollbaren Bereich an.
            </p>
            
            <TermsOfServiceAgreement
              checked={termsAccepted}
              onChange={handleTermsChange}
              showFullText={true}
              error={showError ? "Sie müssen die AGB akzeptieren, um fortzufahren." : undefined}
            />
          </div>
        </div>

        {/* Aktionsbereich */}
        <div className="mt-8 rounded-xl bg-white p-6 shadow-lg">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Registrierung abschließen
          </h2>
          <p className="mb-4 text-gray-600">
            Simuliert einen Registrierungsprozess, bei dem die AGB akzeptiert werden müssen.
          </p>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Status: {termsAccepted ? (
                <span className="font-medium text-green-600">AGB akzeptiert ✓</span>
              ) : (
                <span className="font-medium text-red-600">AGB nicht akzeptiert</span>
              )}
            </div>
            
            <Button
              onClick={handleSubmit}
              className={`${
                termsAccepted 
                  ? "bg-primary hover:bg-primary/90" 
                  : "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
              }`}
            >
              Registrierung abschließen
            </Button>
          </div>
        </div>

        {/* Verwendungsbeispiele */}
        <div className="mt-8 rounded-xl bg-white p-6 shadow-lg">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Verwendung in Formularen
          </h2>
          <p className="mb-4 text-gray-600">
            So können Sie die Komponente in Ihren Registrierungsformularen verwenden:
          </p>
          
          <div className="rounded-lg bg-gray-100 p-4">
            <pre className="text-sm text-gray-800">
{`// Einfache Verwendung
<TermsOfServiceAgreement
  checked={termsAccepted}
  onChange={setTermsAccepted}
/>

// Mit Validierung
<TermsOfServiceAgreement
  checked={termsAccepted}
  onChange={setTermsAccepted}
  error={errors.terms?.message}
/>

// Mit vollständigem Text
<TermsOfServiceAgreement
  checked={termsAccepted}
  onChange={setTermsAccepted}
  showFullText={true}
/>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AGBDemoPage;
