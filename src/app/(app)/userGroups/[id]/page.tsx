import Card from "../../../../component/card";
import prisma from "../../../../server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect, notFound } from "next/navigation";
import Link from "next/link";
import { BsPencilSquare } from "react-icons/bs";
import UserGroupTarifs from "../components/UserGroupTarifs";
import Headline from "~/component/Headline";
import UserTable from "~/app/(app)/users/compontent/userTable";
import { hasRole } from "~/utils/user/roleLabels";

interface Props {
  params: {
    id: string;
  };
}

const getUserGroup = async (id: string) => {
  const session = await getServerSession(authOptions);

  const userRole = session?.user?.role;
  if (!userRole || ![Role.ADMIN, Role.CARD_MANAGER].includes(userRole)) {
    return null;
  }

  const userGroup = await prisma.userGroup.findUnique({
    where: { id },
    include: {
      ou: {
        select: {
          id: true,
          name: true,
        },
      },
      users: {
        select: {
          id: true,
          name: true,
          lastName: true,
          email: true,
          role: true,
          createdAt: true,
          userGroup: true,
          ou: { select: { name: true } },
        },
        orderBy: {
          email: "asc",
        },
      },
      physicalCards: {
        select: {
          uid: true,
          visualNumber: true,
          valid: true,
          EMPCard: {
            select: {
              id: true,
              active: true,
              user: {
                select: {
                  email: true,
                },
              },
            },
          },
        },
        orderBy: {
          visualNumber: "asc",
        },
      },
      companyTarifs: {
        include: {
          tarif: true,
        },
      },
    },
  });

  // Check permissions
  if (
    userGroup &&
    session.user.role !== Role.ADMIN &&
    userGroup.ouId !== session.user.selectedOu.id
  ) {
    return null;
  }

  return userGroup;
};

const UserGroupDetailPage = async ({ params }: Props) => {
  const session = await getServerSession(authOptions);

  if (!session || !hasRole(session.user.role, [Role.ADMIN, Role.CARD_MANAGER])) {
    redirect("/");
  }

  const userGroup = await getUserGroup(params.id);

  if (!userGroup) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <Headline title={`Nutzergruppe: ${userGroup.name}`} />
      <Card header_left="Grundinformationen">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* Name */}
              <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 dark:bg-primary/20 flex h-10 w-10 items-center justify-center rounded-lg">
                    <svg
                      className="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                    <dd className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                      {userGroup.name}
                    </dd>
                  </div>
                </div>
              </div>

              {/* OU */}
              <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 dark:bg-primary/20 flex h-10 w-10 items-center justify-center rounded-lg">
                    <svg
                      className="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Organisationseinheit
                    </dt>
                    <dd className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                      {userGroup.ou.name}
                    </dd>
                  </div>
                </div>
              </div>

              {/* Erstellt am */}
              <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 dark:bg-primary/20 flex h-10 w-10 items-center justify-center rounded-lg">
                    <svg
                      className="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Erstellt am
                    </dt>
                    <dd className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                      {new Date(userGroup.createdAt).toLocaleDateString("de-DE", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </dd>
                  </div>
                </div>
              </div>

              {/* Statistiken */}
              <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
                <div className="flex items-center space-x-3">
                  <div className="bg-primary/10 dark:bg-primary/20 flex h-10 w-10 items-center justify-center rounded-lg">
                    <svg
                      className="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Zuordnungen
                    </dt>
                    <dd className="mt-1 flex space-x-4 text-sm text-gray-900 dark:text-white">
                      <span className="bg-primary/10 dark:bg-primary/20 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium text-primary">
                        {userGroup.users.length} Benutzer
                      </span>
                      <span className="bg-primary/10 dark:bg-primary/20 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium text-primary">
                        {userGroup.physicalCards.length} Karten
                      </span>
                    </dd>
                  </div>
                </div>
              </div>
            </div>

            {/* Beschreibung - falls vorhanden */}
            {userGroup.description && (
              <div className="bg-primary/5 dark:bg-primary/10 mt-6 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-primary/10 dark:bg-primary/20 flex h-10 w-10 items-center justify-center rounded-lg">
                    <svg
                      className="h-5 w-5 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <dt className="text-sm font-medium text-primary">Beschreibung</dt>
                    <dd className="mt-2 text-sm text-gray-700 dark:text-gray-300">
                      {userGroup.description}
                    </dd>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Bearbeiten Button */}
          <div className="ml-6 flex-shrink-0">
            <Link href={`/userGroups/${userGroup.id}/edit`}>
              <button className="btn btn-secondary flex items-center">
                <BsPencilSquare className="mr-2" />
                Bearbeiten
              </button>
            </Link>
          </div>
        </div>
      </Card>

      {/* Users */}
      <Card header_left={`Benutzer (${userGroup.users.length})`}>
        {userGroup.users.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400">Keine Benutzer in dieser Nutzergruppe.</p>
        ) : (
          <UserTable users={userGroup.users}></UserTable>
        )}
      </Card>

      {/* Physical Cards */}
      <Card header_left={`Physische Karten (${userGroup.physicalCards.length})`}>
        {userGroup.physicalCards.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400">
            Keine physischen Karten in dieser Nutzergruppe.
          </p>
        ) : (
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Kartennummer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    UID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                    Zugeordnet an
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-600 dark:bg-gray-800">
                {userGroup.physicalCards.map((card) => (
                  <tr key={card.uid}>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                      {card.visualNumber}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {card.uid}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                          card.valid
                            ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                            : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                        }`}
                      >
                        {card.valid ? "Gültig" : "Ungültig"}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                      {card.EMPCard?.user?.email || "-"}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      {/* Company Tarifs */}
      <UserGroupTarifs userGroupId={userGroup.id} userGroupName={userGroup.name} />
    </div>
  );
};

export default UserGroupDetailPage;
