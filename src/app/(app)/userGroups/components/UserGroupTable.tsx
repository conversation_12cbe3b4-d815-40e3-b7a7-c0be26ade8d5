"use client";
import Table from "../../../../utils/table/table";
import type { Prisma } from "@prisma/client";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import type { ICellRendererParams } from "ag-grid-community";
import Link from "next/link";
import { HiMagnifyingGlass } from "react-icons/hi2";
import { BsPencilSquare } from "react-icons/bs";
import React from "react";

type UserGroupWithIncludes = Prisma.UserGroupGetPayload<{
  include: {
    ou: {
      select: {
        id: true;
        name: true;
      };
    };
    _count: {
      select: {
        users: true;
        physicalCards: true;
        companyTarifs: true;
      };
    };
  };
}>;

interface Props {
  data: UserGroupWithIncludes[];
}

const UserGroupTable = ({ data }: Props) => {
  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <div className="flex gap-2">
        <Link href={`/userGroups/${params?.data?.id}`}>
          <HiMagnifyingGlass className="cursor-pointer text-blue-600 hover:text-blue-800" />
        </Link>
        <Link href={`/userGroups/${params?.data?.id}/edit`}>
          <BsPencilSquare className="cursor-pointer text-green-600 hover:text-green-800" />
        </Link>
      </div>
    );
  };

  const columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: "name",
      headerName: "Name",
      flex: 2,
    },
    {
      field: "description",
      headerName: "Beschreibung",
      flex: 3,
      cellRenderer: (params: ICellRendererParams) => {
        return params.value || "-";
      },
    },
    {
      field: "ou.name",
      headerName: "OU",
      flex: 2,
      valueGetter: (params) => params.data?.ou?.name || "-",
    },
    {
      field: "_count.users",
      headerName: "Benutzer",
      flex: 1,
      valueGetter: (params) => params.data?._count?.users || 0,
    },
    {
      field: "_count.physicalCards",
      headerName: "Karten",
      flex: 1,
      valueGetter: (params) => params.data?._count?.physicalCards || 0,
    },
    {
      field: "_count.companyTarifs",
      headerName: "Tarife",
      flex: 1,
      valueGetter: (params) => params.data?._count?.companyTarifs || 0,
    },
    {
      field: "createdAt",
      headerName: "Erstellt",
      flex: 2,
      cellRenderer: (params: ICellRendererParams) => {
        if (!params.value) return "-";
        return new Date(params.value).toLocaleDateString("de-DE");
      },
    },
    {
      field: "action",
      headerName: "Aktionen",
      cellRenderer: ActionCellRenderer,
      flex: 1,
      sortable: false,
      filter: false,
    },
  ];

  return (
    <div className={"h-[80vh] min-h-[500px]"}>
      <Table columnDefs={columnDefs} rowData={data} />
    </div>
  );
};

export default UserGroupTable;
