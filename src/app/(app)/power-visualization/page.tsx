import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import PowerVisualization from "./components/PowerVisualization";
import { Suspense } from "react";
import Loading from "~/app/(app)/loading";

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.role || (session.user.role !== Role.ADMIN && session.user.role !== Role.CPO)) {
    return <NotFound />;
  }

  return (
    <div className="space-y-6">
      <Suspense fallback={<Loading />}>
        <PowerVisualization />
      </Suspense>
    </div>
  );
};

export default Page;
