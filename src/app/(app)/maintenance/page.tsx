import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import MaintenanceManager from "./components/MaintenanceManager";

import Loading from "~/app/(app)/maintenance/loading";
import { Suspense } from "react";

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.role || (session.user.role !== Role.ADMIN && session.user.role !== Role.CPO)) {
    return <NotFound />;
  }
  return <MaintenanceManager />;
};

export default Page;
