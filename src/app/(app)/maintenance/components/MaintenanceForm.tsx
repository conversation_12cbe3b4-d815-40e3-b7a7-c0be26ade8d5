"use client";

import { useState, useEffect } from "react";
import { MaintenanceTargetType } from "@prisma/client";
import Button from "~/component/button";
import { AiOutlineClose, AiOutlineSave } from "react-icons/ai";

interface MaintenanceCategory {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
}

interface MaintenanceRecord {
  id: string;
  date: string;
  description: string;
  notes?: string;
  targetType: MaintenanceTargetType;
  locationId?: string;
  evseId?: string;
  connectorId?: string;
  category: {
    id: string;
    name: string;
  };
}

interface Location {
  id: string;
  name: string;
  city: string;
  street: string;
  evses: {
    uid: string;
    evse_id: string;
    status: string;
  }[];
}

interface Props {
  categories: MaintenanceCategory[];
  locations: Location[];
  editingRecord?: MaintenanceRecord | null;
  onSubmit: () => void;
  onCancel: () => void;
}

const MaintenanceForm = ({ categories, locations, editingRecord, onSubmit, onCancel }: Props) => {
  const [formData, setFormData] = useState({
    date: "",
    description: "",
    notes: "",
    targetType: MaintenanceTargetType.LOCATION,
    locationId: "",
    evseId: "",
    connectorId: "",
    categoryId: "",
  });
  const [loading, setLoading] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);

  useEffect(() => {
    if (editingRecord) {
      const editdate = editingRecord.date.split("T")[0];
      if (editdate) {
        setFormData({
          date: editdate, // Format for date input
          description: editingRecord.description,
          notes: editingRecord.notes || "",
          targetType: editingRecord.targetType,
          locationId: editingRecord.locationId || "",
          evseId: editingRecord.evseId || "",
          connectorId: editingRecord.connectorId || "",
          categoryId: editingRecord.category.id,
        });
      }

      if (editingRecord.locationId) {
        const location = locations.find((l) => l.id === editingRecord.locationId);
        setSelectedLocation(location || null);
      }
    } else {
      // Set default date to today
      const today = new Date().toISOString().split("T")[0];
      if (today) {
        setFormData((prev) => ({ ...prev, date: today }));
      }
    }
  }, [editingRecord, locations]);

  const handleLocationChange = (locationId: string) => {
    const location = locations.find((l) => l.id === locationId);
    setSelectedLocation(location || null);
    setFormData((prev) => ({
      ...prev,
      locationId,
      evseId: "", // Reset EVSE selection when location changes
      connectorId: "",
    }));
  };

  const handleTargetTypeChange = (targetType: MaintenanceTargetType) => {
    setFormData((prev) => ({
      ...prev,
      targetType,
      locationId: targetType === MaintenanceTargetType.ALL_EVSES ? "" : prev.locationId,
      evseId:
        targetType === MaintenanceTargetType.LOCATION ||
        targetType === MaintenanceTargetType.ALL_EVSES
          ? ""
          : prev.evseId,
      connectorId: targetType !== MaintenanceTargetType.CONNECTOR ? "" : prev.connectorId,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const url = editingRecord
        ? `/api/maintenance/records/${editingRecord.id}`
        : "/api/maintenance/records";

      const method = editingRecord ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        onSubmit();
      } else {
        const error = await response.json();
        alert(`Fehler: ${error.error}`);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Fehler beim Speichern");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white shadow-lg">
        <div className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <h3 className="text-lg font-bold text-primary">
              {editingRecord ? "Wartung bearbeiten" : "Neue Wartung eintragen"}
            </h3>
            <button onClick={onCancel} className="text-gray-500 hover:text-gray-700">
              <AiOutlineClose size={24} />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Date */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">Datum *</label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData((prev) => ({ ...prev, date: e.target.value }))}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                required
              />
            </div>

            {/* Category */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">Kategorie *</label>
              <select
                value={formData.categoryId}
                onChange={(e) => setFormData((prev) => ({ ...prev, categoryId: e.target.value }))}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                required
              >
                <option value="">Kategorie auswählen</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Target Type */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">
                Wartungsbereich *
              </label>
              <select
                value={formData.targetType}
                onChange={(e) => handleTargetTypeChange(e.target.value as MaintenanceTargetType)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                required
              >
                <option value={MaintenanceTargetType.LOCATION}>Standort</option>
                <option value={MaintenanceTargetType.EVSE}>Einzelne Ladesäule (EVSE-ID)</option>
                <option value={MaintenanceTargetType.CONNECTOR}>Connector</option>
                <option value={MaintenanceTargetType.ALL_EVSES}>Alle Ladesäulen</option>
              </select>
            </div>

            {/* Location Selection */}
            {formData.targetType !== MaintenanceTargetType.ALL_EVSES && (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Standort {formData.targetType !== MaintenanceTargetType.LOCATION ? "" : "*"}
                </label>
                <select
                  value={formData.locationId}
                  onChange={(e) => handleLocationChange(e.target.value)}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                  required={formData.targetType === MaintenanceTargetType.LOCATION}
                >
                  <option value="">Standort auswählen</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.id}>
                      {location.name} - {location.city}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* EVSE Selection */}
            {(formData.targetType === MaintenanceTargetType.EVSE ||
              formData.targetType === MaintenanceTargetType.CONNECTOR) &&
              selectedLocation && (
                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700">EVSE-ID *</label>
                  <select
                    value={formData.evseId}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, evseId: e.target.value, connectorId: "" }))
                    }
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                    required
                  >
                    <option value="">EVSE auswählen</option>
                    {selectedLocation.evses.map((evse) => (
                      <option key={evse.uid} value={evse.uid}>
                        {evse.evse_id} ({evse.status})
                      </option>
                    ))}
                  </select>
                </div>
              )}

            {/* Connector ID */}
            {formData.targetType === MaintenanceTargetType.CONNECTOR && (
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">Connector ID</label>
                <input
                  type="text"
                  value={formData.connectorId}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, connectorId: e.target.value }))
                  }
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                  placeholder="z.B. 1, 2"
                />
              </div>
            )}

            {/* Description */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">Beschreibung *</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                rows={3}
                required
                placeholder="Was wurde gemacht?"
              />
            </div>

            {/* Notes */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">Notizen</label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
                rows={2}
                placeholder="Zusätzliche Informationen..."
              />
            </div>

            {/* Submit buttons */}
            <div className="flex flex-col gap-2 pt-4 sm:flex-row">
              <Button
                type="submit"
                disabled={loading}
                className="flex items-center justify-center gap-2"
              >
                <AiOutlineSave size={16} />
                {loading ? "Speichern..." : "Speichern"}
              </Button>
              <Button type="button" onClick={onCancel} className="bg-gray-500 hover:bg-gray-600">
                Abbrechen
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceForm;
