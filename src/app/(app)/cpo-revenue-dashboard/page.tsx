import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Headline from "~/component/Headline";
import CPORevenueDashboard from "./components/CPORevenueDashboard";

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return <NotFound />;
  }

  return (
    <>
      <Headline title="CPO Revenue Dashboard" />
      <div className="mb-4">
        <p className="text-gray-600 dark:text-gray-400">
          Übersicht über Einnahmen durch CPO-Verträge. Vergleich von Gutschriften und Rechnungen 
          mit Aufschlüsselung nach Onboarding- und wiederkehrenden Gebühren.
        </p>
      </div>
      <CPORevenueDashboard />
    </>
  );
};

export default Page;
