"use client";

import React from "react";
import ReactECharts from "echarts-for-react";

interface CPOGlobalTrendChartProps {
  monthlyData: {
    month: string;
    totalRecurringRevenue: number;
  }[];
  yearForecast: number;
  totalOnboardingRevenue: number;
}

const CPOGlobalTrendChart = ({ monthlyData, yearForecast, totalOnboardingRevenue }: CPOGlobalTrendChartProps) => {
  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
      "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  // Generate all months from the earliest data point to current month + 12 months
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  // Find earliest month in data
  const sortedData = monthlyData
    .filter(d => d.totalRecurringRevenue > 0)
    .sort((a, b) => a.month.localeCompare(b.month));

  if (sortedData.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-gray-500">
        Keine Daten für Trend-Analyse verfügbar
      </div>
    );
  }

  const [earliestYear, earliestMonth] = sortedData[0].month.split("-").map(Number);
  const allMonths: string[] = [];

  // Generate months from earliest to current + 12 months
  for (let year = earliestYear; year <= currentYear + 1; year++) {
    const startMonth = year === earliestYear ? earliestMonth : 1;
    const endMonth = year === currentYear + 1 ? 12 : (year === currentYear ? currentMonth + 12 : 12);

    for (let month = startMonth; month <= endMonth; month++) {
      allMonths.push(`${year}-${String(month).padStart(2, "0")}`);
    }
  }

  // Calculate trend line using linear regression
  const calculateTrendLine = (data: { x: number; y: number }[]) => {
    const n = data.length;
    const sumX = data.reduce((sum, point) => sum + point.x, 0);
    const sumY = data.reduce((sum, point) => sum + point.y, 0);
    const sumXY = data.reduce((sum, point) => sum + point.x * point.y, 0);
    const sumXX = data.reduce((sum, point) => sum + point.x * point.x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return { slope, intercept };
  };

  // Prepare data for trend calculation (only months with actual data)
  const trendData = sortedData.map((d, index) => ({
    x: index + 1,
    y: d.totalRecurringRevenue,
  }));

  const { slope, intercept } = calculateTrendLine(trendData);

  // Map actual data and trend line
  const actualData: (number | null)[] = [];
  const trendLineData: (number | null)[] = [];

  // Create a mapping of months to their sequential position in the trend
  const monthToTrendIndex = new Map<string, number>();
  sortedData.forEach((d, index) => {
    monthToTrendIndex.set(d.month, index + 1);
  });

  allMonths.forEach((month, index) => {
    const monthNum = parseInt(month.split("-")[1]);
    const existingData = monthlyData.find(d => d.month === month);

    if (existingData && existingData.totalRecurringRevenue > 0) {
      actualData.push(existingData.totalRecurringRevenue);
      
      // Calculate trend line value for this month
      const trendIndex = monthToTrendIndex.get(month);
      if (trendIndex) {
        const trendValue = slope * trendIndex + intercept;
        trendLineData.push(Math.max(0, trendValue));
      } else {
        trendLineData.push(null);
      }
    } else {
      actualData.push(null);
      trendLineData.push(null);
    }
  });

  const options = {
    title: {
      text: `Globaler CPO Revenue Trend & Jahresprognose (${formatCurrency(yearForecast)})`,
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: function(params: any) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`;
        params.forEach((param: any) => {
          if (param.value !== null) {
            result += `${param.seriesName}: ${formatCurrency(param.value)}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: ["Wiederkehrende Einnahmen", "Trendlinie"],
      top: 35,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "20%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: allMonths.map(formatMonth),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
      },
    },
    yAxis: {
      type: "value",
      name: "Einnahmen (€)",
      nameLocation: "middle",
      nameGap: 50,
      axisLabel: {
        formatter: function(value: number) {
          return formatCurrency(value);
        },
        fontSize: 10,
      },
    },
    series: [
      {
        name: "Wiederkehrende Einnahmen",
        type: "line",
        data: actualData,
        lineStyle: {
          color: "#3b82f6",
          width: 3,
        },
        itemStyle: {
          color: "#3b82f6",
        },
        symbol: "circle",
        symbolSize: 6,
        connectNulls: false,
      },
      {
        name: "Trendlinie",
        type: "line",
        data: trendLineData,
        lineStyle: {
          color: "#10b981",
          width: 2,
          type: "dashed",
        },
        itemStyle: {
          color: "#10b981",
        },
        symbol: "none",
        symbolSize: 0,
        connectNulls: true,
        smooth: true,
      },
    ],
  };

  return (
    <div className="h-80">
      <ReactECharts option={options} style={{ height: "100%", width: "100%" }} />
    </div>
  );
};

export default CPOGlobalTrendChart;
