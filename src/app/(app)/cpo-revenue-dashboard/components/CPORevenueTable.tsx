"use client";

import React, { useState } from "react";
import Card from "~/component/card";
import {
  FaChevronDown,
  FaChevronRight,
  FaArrowUp,
  FaCalendar,
  FaFileAlt,
  FaArrowDown,
} from "react-icons/fa";
import type { CPORevenueData } from "~/app/api/finance-dashboard/route";
import CPORevenueChart from "./CPORevenueChart";

interface CPORevenueTableProps {
  cpos: CPORevenueData[];
}

const CPORevenueTable = ({ cpos }: CPORevenueTableProps) => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRow = (cpoId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(cpoId)) {
      newExpanded.delete(cpoId);
    } else {
      newExpanded.add(cpoId);
    }
    setExpandedRows(newExpanded);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan",
      "Feb",
      "Mär",
      "Apr",
      "Mai",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Okt",
      "Nov",
      "Dez",
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  if (cpos.length === 0) {
    return (
      <Card>
        <div className="py-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Keine CPO-Daten gefunden</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      header_left={
        <div className="flex items-center gap-2">
          <FaArrowUp className="h-5 w-5" />
          CPO Revenue Details
        </div>
      }
    >
      <div className="space-y-2">
        {cpos.map((cpo) => (
          <div key={cpo.cpoId} className="rounded-lg border">
            {/* Main Row */}
            <div
              className="cursor-pointer border-l-4 border-transparent p-4 transition-colors duration-200 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900"
              onClick={() => toggleRow(cpo.cpoId)}
              title="Klicken für Details"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <button className="flex h-6 w-6 items-center justify-center rounded p-1 hover:bg-gray-100">
                    {expandedRows.has(cpo.cpoId) ? (
                      <FaChevronDown className="h-3 w-3" />
                    ) : (
                      <FaChevronRight className="h-3 w-3" />
                    )}
                  </button>
                  <div>
                    <h3 className="flex items-center gap-2 text-lg font-semibold">
                      {cpo.cpoName}
                      <span className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-600">
                        {expandedRows.has(cpo.cpoId) ? "Details ausblenden" : "Details anzeigen"}
                      </span>
                    </h3>
                    <p className="text-sm text-gray-500">ID: {cpo.cpoId}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-right md:grid-cols-5">
                  <div>
                    <p className="text-sm text-gray-500">Gutschriften</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {formatCurrency(cpo.totalCreditNotes)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Rechnungen</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {formatCurrency(cpo.totalInvoiced)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Onboarding</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {formatCurrency(cpo.onboardingRevenue)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Wiederkehrend</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {formatCurrency(cpo.recurringRevenue)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Jahresprognose</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {formatCurrency(cpo.yearForecast)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Expanded Content */}
            {expandedRows.has(cpo.cpoId) && (
              <div className="border-t bg-gray-50 p-4 dark:bg-gray-800">
                <div className="space-y-6">
                  {/* Revenue Chart */}
                  <div>
                    <div className="mb-3 flex items-center gap-2">
                      <FaArrowUp className="h-4 w-4" />
                      <h4 className="font-semibold">Wiederkehrende Einnahmen & Prognose</h4>
                    </div>
                    <div className="rounded border bg-white p-4 dark:bg-gray-700">
                      <CPORevenueChart
                        monthlyData={cpo.monthlyBreakdown}
                        yearForecast={cpo.yearForecast}
                        onboardingRevenue={cpo.onboardingRevenue}
                      />
                    </div>
                  </div>

                  {/* Invoice Details */}
                  <div>
                    <div className="mb-3 flex items-center gap-2">
                      <FaFileAlt className="h-4 w-4" />
                      <h4 className="font-semibold">Rechnungen ({cpo.invoiceDetails.length})</h4>
                    </div>
                    <div className="overflow-hidden rounded border bg-white dark:bg-gray-700">
                      <div className="max-h-64 overflow-y-auto">
                        {cpo.invoiceDetails.length > 0 ? (
                          <table className="w-full text-sm">
                            <thead className="sticky top-0 bg-gray-100 dark:bg-gray-600">
                              <tr>
                                <th className="p-2 text-left">Rechnungsnr.</th>
                                <th className="p-2 text-left">Datum</th>
                                <th className="p-2 text-right">Betrag</th>
                                <th className="p-2 text-center">Typ</th>
                              </tr>
                            </thead>
                            <tbody>
                              {cpo.invoiceDetails.map((invoice) => (
                                <tr
                                  key={invoice.id}
                                  className="border-t hover:bg-gray-50 dark:hover:bg-gray-600"
                                >
                                  <td className="p-2">{invoice.invoiceNumber || "N/A"}</td>
                                  <td className="p-2">
                                    {invoice.invoiceDate
                                      ? new Date(invoice.invoiceDate).toLocaleDateString("de-DE")
                                      : "N/A"}
                                  </td>
                                  <td className="p-2 text-right font-semibold">
                                    {new Intl.NumberFormat("de-DE", {
                                      style: "currency",
                                      currency: "EUR",
                                    }).format(invoice.sumGross)}
                                  </td>
                                  <td className="p-2 text-center">
                                    <span
                                      className={`rounded px-2 py-1 text-xs ${
                                        invoice.isOnboarding
                                          ? "bg-purple-100 text-purple-800"
                                          : "bg-blue-100 text-blue-800"
                                      }`}
                                    >
                                      {invoice.isOnboarding ? "Onboarding" : "Wiederkehrend"}
                                    </span>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        ) : (
                          <p className="py-4 text-center text-gray-500">
                            Keine Rechnungen gefunden
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Credit Note Details */}
                  {cpo.creditNoteDetails.length > 0 && (
                    <div>
                      <div className="mb-3 flex items-center gap-2">
                        <FaArrowDown className="h-4 w-4" />
                        <h4 className="font-semibold">
                          Gutschriften ({cpo.creditNoteDetails.length})
                        </h4>
                      </div>
                      <div className="overflow-hidden rounded border bg-white dark:bg-gray-700">
                        <div className="max-h-64 overflow-y-auto">
                          <table className="w-full text-sm">
                            <thead className="sticky top-0 bg-red-100 dark:bg-red-900">
                              <tr>
                                <th className="p-2 text-left">Gutschriftsnr.</th>
                                <th className="p-2 text-left">Datum</th>
                                <th className="p-2 text-right">Betrag</th>
                              </tr>
                            </thead>
                            <tbody>
                              {cpo.creditNoteDetails.map((creditNote) => (
                                <tr
                                  key={creditNote.id}
                                  className="border-t hover:bg-gray-50 dark:hover:bg-gray-600"
                                >
                                  <td className="p-2">{creditNote.invoiceNumber || "N/A"}</td>
                                  <td className="p-2">
                                    {creditNote.invoiceDate
                                      ? new Date(creditNote.invoiceDate).toLocaleDateString("de-DE")
                                      : "N/A"}
                                  </td>
                                  <td className="p-2 text-right font-semibold text-red-600">
                                    {new Intl.NumberFormat("de-DE", {
                                      style: "currency",
                                      currency: "EUR",
                                    }).format(creditNote.sumGross)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Monthly Breakdown */}
                  <div>
                    <div className="mb-3 flex items-center gap-2">
                      <FaCalendar className="h-4 w-4" />
                      <h4 className="font-semibold">Monatliche Aufschlüsselung</h4>
                    </div>

                    {cpo.monthlyBreakdown.length > 0 ? (
                      <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
                        {cpo.monthlyBreakdown.slice(0, 12).map((month) => (
                          <div
                            key={month.month}
                            className="rounded border bg-white p-3 dark:bg-gray-700"
                          >
                            <div className="mb-2 flex items-center justify-between">
                              <span className="font-medium">{formatMonth(month.month)}</span>
                            </div>
                            <div className="space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Rechnungen:</span>
                                <span className="text-blue-600">
                                  {formatCurrency(month.invoiced)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Gutschriften:</span>
                                <span className="text-red-600">
                                  {formatCurrency(month.creditNotes)}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="py-4 text-center text-gray-500">
                        Keine monatlichen Daten verfügbar
                      </p>
                    )}

                    {/* Revenue Breakdown */}
                    <div className="mt-6 border-t pt-4">
                      <h4 className="mb-3 font-semibold">Umsatzaufschlüsselung</h4>
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div className="rounded border bg-white p-3 text-center dark:bg-gray-700">
                          <p className="text-sm text-gray-500">Onboarding Anteil</p>
                          <p className="text-lg font-semibold text-purple-600">
                            {cpo.totalInvoiced > 0
                              ? `${((cpo.onboardingRevenue / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"}
                          </p>
                        </div>
                        <div className="rounded border bg-white p-3 text-center dark:bg-gray-700">
                          <p className="text-sm text-gray-500">Wiederkehrend Anteil</p>
                          <p className="text-lg font-semibold text-orange-600">
                            {cpo.totalInvoiced > 0
                              ? `${((cpo.recurringRevenue / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"}
                          </p>
                        </div>
                        <div className="rounded border bg-white p-3 text-center dark:bg-gray-700">
                          <p className="text-sm text-gray-500">Gutschrift Rate</p>
                          <p className="text-lg font-semibold text-red-600">
                            {cpo.totalInvoiced > 0
                              ? `${((cpo.totalCreditNotes / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </Card>
  );
};

export default CPORevenueTable;
