const InfoPanelEnum = {
  INFO: "INFO",
  WARNING: "WARNING",
  ERROR: "ERROR",
  SUCCESS: "SUCCESS",
  DEFAULT: "DEFAULT",
  NEUTRAL: "NEUTRAL",
};

interface Props {
  type: typeof InfoPanelEnum;
  headLine: string;
  text: string;
}

const InfoPanel = ({ type }) => {
  const colorConfig = {
    [InfoPanelEnum.INFO]: "bg-blue-50",
    [InfoPanelEnum.WARNING]: "bg-yellow-50",
    [InfoPanelEnum.ERROR]: "bg-red-50",
    [InfoPanelEnum.SUCCESS]: "bg-green-50",
    [InfoPanelEnum.DEFAULT]: "bg-gray-50",
    [InfoPanelEnum.NEUTRAL]: "bg-gray-50",
  };
  const textColorConfig = {
    [InfoPanelEnum.INFO]: "text-blue-800",
    [InfoPanelEnum.WARNING]: "text-yellow-800",
    [InfoPanelEnum.ERROR]: "text-red-800",
    [InfoPanelEnum.SUCCESS]: "text-green-800",
    [InfoPanelEnum.DEFAULT]: "text-gray-800",
    [InfoPanelEnum.NEUTRAL]: "text-gray-800",
  };
  const iconConfig = {
    [InfoPanelEnum.INFO]: "info",
    [InfoPanelEnum.WARNING]: "warning",
    [InfoPanelEnum.ERROR]: "error",
    [InfoPanelEnum.SUCCESS]: "success",
    [InfoPanelEnum.DEFAULT]: "default",
    [InfoPanelEnum.NEUTRAL]: "default",
  };

  return (
    <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className=`h-5 w-5 text-blue-400` viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
            Zweck von Nutzergruppen
          </h3>
          <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
            <p>
              Nutzergruppen sind dafür gedacht, Benutzern bestimmte Tarife zugänglich zu machen.
              Benutzer werden Nutzergruppen zugeordnet, zum Beispiel <strong>Mitarbeitern</strong>,{" "}
              <strong>Mietern</strong> oder <strong>ansässigen Geschäftspartnern</strong>.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InfoPanel;
