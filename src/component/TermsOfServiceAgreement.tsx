"use client";

import React, { useState } from "react";

interface TermsOfServiceAgreementProps {
  /** Whether the checkbox is checked */
  checked?: boolean;
  /** Callback when checkbox state changes */
  onChange?: (checked: boolean) => void;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Error message to display */
  error?: string;
  /** Custom CSS classes */
  className?: string;
  /** Whether to show the full terms text or just a link */
  showFullText?: boolean;
  /** Custom terms text (optional) */
  customTermsText?: string;
  /** Link to external terms page */
  termsLink?: string;
}

const TermsOfServiceAgreement: React.FC<TermsOfServiceAgreementProps> = ({
  checked = false,
  onChange,
  disabled = false,
  error,
  className = "",
  showFullText = false,
  customTermsText,
  termsLink = "/agb",
}) => {
  const [internalChecked, setInternalChecked] = useState(checked);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newChecked = event.target.checked;
    setInternalChecked(newChecked);
    onChange?.(newChecked);
  };

  const isChecked = onChange ? checked : internalChecked;

  const defaultTermsText = `
    Allgemeine Geschäftsbedingungen (AGB)

    1. Geltungsbereich
    Diese Allgemeinen Geschäftsbedingungen gelten für alle Verträge zwischen dem Betreiber der Ladeinfrastruktur und den Nutzern des Ladeportals.

    2. Vertragsschluss
    Der Vertrag kommt durch die Registrierung im Ladeportal und die Bestätigung dieser AGB zustande.

    3. Leistungen
    Der Betreiber stellt Ladeinfrastruktur für Elektrofahrzeuge zur Verfügung. Die Abrechnung erfolgt nach den jeweils gültigen Tarifen.

    4. Zahlungsbedingungen
    Die Abrechnung erfolgt monatlich per SEPA-Lastschriftverfahren. Der Nutzer ist verpflichtet, ein gültiges SEPA-Lastschriftmandat zu erteilen.

    5. Haftung
    Der Betreiber haftet nur für Schäden, die vorsätzlich oder grob fahrlässig verursacht wurden.

    6. Datenschutz
    Die Verarbeitung personenbezogener Daten erfolgt gemäß der Datenschutzerklärung.

    7. Kündigung
    Der Vertrag kann von beiden Seiten mit einer Frist von 30 Tagen zum Monatsende gekündigt werden.

    8. Schlussbestimmungen
    Es gilt deutsches Recht. Gerichtsstand ist der Sitz des Betreibers.
  `;

  const termsContent = customTermsText || defaultTermsText;

  return (
    <div className={`w-full ${className}`}>
      {showFullText && (
        <div className="mb-4 max-h-60 overflow-y-auto rounded-lg border border-gray-300 bg-gray-50 p-4 text-sm">
          <pre className="whitespace-pre-wrap font-sans text-gray-700">
            {termsContent}
          </pre>
        </div>
      )}

      <div className="flex items-start space-x-3">
        <input
          id="terms-checkbox"
          type="checkbox"
          checked={isChecked}
          onChange={handleChange}
          disabled={disabled}
          className={`
            relative float-left mt-1 h-5 w-5 cursor-pointer appearance-none rounded-1.4
            border border-solid border-slate-150 bg-white bg-contain bg-center bg-no-repeat
            align-top text-base transition-all duration-250 ease-soft
            after:absolute after:flex after:h-full after:w-full after:items-center
            after:justify-center after:text-xxs after:text-white after:opacity-0
            after:transition-all after:duration-250 after:ease-soft-in-out
            after:content-['✓'] checked:bg-primary checked:after:opacity-100
            focus:shadow-soft-primary-outline focus:outline-none
            disabled:cursor-not-allowed disabled:opacity-50
            ${error ? "border-red-500" : "border-slate-150"}
          `}
        />
        <label
          htmlFor="terms-checkbox"
          className={`cursor-pointer select-none text-sm leading-relaxed ${
            disabled ? "cursor-not-allowed opacity-50" : ""
          } ${error ? "text-red-700" : "text-gray-700"}`}
        >
          Ich habe die{" "}
          {showFullText ? (
            <span className="font-medium text-primary">
              Allgemeinen Geschäftsbedingungen
            </span>
          ) : (
            <a
              href={termsLink}
              target="_blank"
              rel="noopener noreferrer"
              className="font-medium text-primary underline hover:text-primary/80"
              onClick={(e) => e.stopPropagation()}
            >
              Allgemeinen Geschäftsbedingungen
            </a>
          )}{" "}
          gelesen und akzeptiere diese.
        </label>
      </div>

      {error && (
        <p className="mt-2 text-sm text-red-600">
          {error}
        </p>
      )}
    </div>
  );
};

export default TermsOfServiceAgreement;
